package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;

import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeResponse;
import com.smartcar.easylauncher.data.model.theme.api.ThemeListModel;
import com.smartcar.easylauncher.shared.adapter.theme.ThemeBannerAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneThemeDetailBinding;
import com.smartcar.easylauncher.data.database.dbmager.SimplifiedThemeDbManager;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.data.model.theme.ThemeBannerModel;
import com.smartcar.easylauncher.data.model.theme.api.ApiResponse;
import com.smartcar.easylauncher.core.constants.Const;
import com.google.gson.reflect.TypeToken;

import rxhttp.RxHttp;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;

import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;
import com.smartcar.easylauncher.shared.view.SecondaryPageActionBar;
import com.zhpan.bannerview.constants.IndicatorGravity;
import com.zhpan.bannerview.constants.PageStyle;
import com.zhpan.bannerview.utils.BannerUtils;
import com.zhpan.indicator.enums.IndicatorSlideMode;
import com.zhpan.indicator.enums.IndicatorStyle;

import java.io.File;

import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import rxhttp.wrapper.cache.CacheMode;


/**
 * 主题详情Scene
 * 显示主题的详细信息，支持预览、下载、应用和删除操作
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class ThemeDetailScene extends BaseScene {

    private static final String TAG = "ThemeDetailScene";
    private static final String KEY_UNIFIED_THEME = "unified_theme";

    // 共享元素动画常量
    public static final String VIEW_NAME_THEME_IMAGE = "theme:detail:image";
    public static final String VIEW_NAME_THEME_TITLE = "theme:detail:title";

    private SceneThemeDetailBinding binding;
    private UnifiedThemeModel themeInfo; // 使用统一主题模型
    private final CompositeDisposable disposables = new CompositeDisposable();
    private SimplifiedThemeDbManager dbManager;
    private boolean isDownloading = false;

    // 统一操作栏组件
    private SecondaryPageActionBar actionBar;

    private ThemeBannerAdapter bannerAdapter;
    private int currentBannerItemCount = 0;

    // 操作状态管理
    private boolean isOperationInProgress = false;
    private long lastClickTime = 0;
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1秒防抖

    // 下载相关
    private Disposable downloadDisposable;
    private String downloadTimeMillis;
    private ThemeListModel.RowsDTO themeListInfo;

    /**
     * 创建主题详情Scene实例 - 使用统一主题模型
     */
    public static ThemeDetailScene newInstance(ThemeListModel.RowsDTO theme) {
        ThemeDetailScene scene = new ThemeDetailScene();
        Bundle args = new Bundle();
        args.putString(KEY_UNIFIED_THEME, new Gson().toJson(theme));
        scene.setArguments(args);
        return scene;
    }

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneThemeDetailBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
        initViews();
        setupSharedElements();
        loadThemeData();
    }

    /**
     * 设置共享元素动画
     */
    private void setupSharedElements() {
        if (themeInfo != null) {
            // 设置轮播图的共享元素名称
            ViewCompat.setTransitionName(binding.bannerViewPager, VIEW_NAME_THEME_IMAGE + themeInfo.getId());
            // 设置主题名称的共享元素名称
            ViewCompat.setTransitionName(binding.tvThemeName, VIEW_NAME_THEME_TITLE + themeInfo.getId());
        }
    }

    /**
     * 初始化数据
     */
    private void initData() {
        dbManager = SimplifiedThemeDbManager.getInstance();

        // 从参数中获取主题信息
        Bundle args = getArguments();
        if (args != null) {
            String themeJson = args.getString(KEY_UNIFIED_THEME);
            if (themeJson != null) {
                themeListInfo = new Gson().fromJson(themeJson, ThemeListModel.RowsDTO.class);
            }
        }

        if (themeListInfo == null) {
            MyLog.e(TAG, "主题信息为空，返回上一页");
            getNavigationScene(this).pop();
            return;
        }

        // 获取最新的主题详情
        loadThemeDetailFromAPI();
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 初始化统一操作栏
        initActionBar();

        // 初始化轮播图
        initBannerView();
    }

    /**
     * 初始化统一操作栏
     */
    private void initActionBar() {
        actionBar = binding.actionBar;

        // 设置返回按钮
        actionBar.setBackAction(() -> getNavigationScene(this).pop());

        // 根据主题状态设置操作按钮
        updateActionBar();
    }

    /**
     * 加载主题数据 - 入口方法
     */
    private void loadThemeData() {
        if (themeInfo == null) return;

        // 直接设置UI
        setupThemeInfo();
        // 新API数据会在loadThemeDetailFromAPI中处理
    }

    /**
     * 设置主题基本信息
     */
    private void setupThemeInfo() {
        if (themeInfo == null) return;

        // 设置主题基本信息
        binding.tvThemeName.setText(themeInfo.getThemeName());
        binding.tvThemeAuthor.setText("作者: " + themeInfo.getAuthor());
        binding.tvThemeDescription.setText(themeInfo.getThemeDescription());

        // 使用themeTypeName，如果为空则根据themeType判断
        String themeType = themeInfo.getThemeTypeName();
        if (themeType == null || themeType.isEmpty()) {
            themeType = themeInfo.getThemeType() == 0 ? "白天主题" : "夜间主题";
        }
        binding.tvThemeType.setText(themeType);
        binding.tvDownloadCount.setText("下载量: " + (themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0));

        // 设置主题标签
        if (themeInfo.getLabel() != null && !themeInfo.getLabel().trim().isEmpty()) {
            binding.tvThemeLabel.setText(themeInfo.getLabel());
            binding.tvThemeLabel.setVisibility(View.VISIBLE);
        } else {
            binding.tvThemeLabel.setVisibility(View.GONE);
        }

        // 设置版本信息（如果有主题包）
        if (themeInfo.getThemePackage() != null) {
            binding.tvThemeVersion.setText("版本: " + themeInfo.getThemePackage().getVersionName());
            binding.tvUpdateVersion.setText("版本 v" + themeInfo.getThemePackage().getVersionName());

            String updateDescription = themeInfo.getThemePackage().getUpdateDescription();
            if (updateDescription != null && !updateDescription.trim().isEmpty()) {
                String formattedContent = formatUpdateContent(updateDescription);
                binding.tvUpdateContent.setText(formattedContent);
            }
        }

        // 更新操作按钮
        updateActionBar();
    }

    /**
     * 格式化更新内容为美观的列表形式
     */
    private String formatUpdateContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        // 如果内容已经包含项目符号，直接返回
        if (content.contains("•") || content.contains("·") || content.contains("-")) {
            return content;
        }

        // 按行分割并添加项目符号
        String[] lines = content.split("\n");
        StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 移除可能存在的数字序号
                line = line.replaceFirst("^\\d+\\.\\s*", "");
                formatted.append("• ").append(line);
                if (i < lines.length - 1) {
                    formatted.append("\n");
                }
            }
        }

        return formatted.toString();
    }

    /**
     * 初始化轮播图
     */
    private void initBannerView() {
        bannerAdapter = new ThemeBannerAdapter();

        // 设置轮播图配置
        setupBannerView();
    }

    /**
     * 设置轮播图动态配置 - 根据图片数量
     */
    private void setupBannerBasicConfig(int itemCount) {

        try {
            // 根据图片数量设置自动播放和循环
            if (itemCount <= 1) {

                binding.bannerViewPager.setAutoPlay(false);
                binding.bannerViewPager.setCanLoop(false);
                binding.bannerViewPager.setIndicatorVisibility(View.GONE); // 单张图片隐藏指示器
                MyLog.d(TAG, "单张图片，禁用自动播放和循环，隐藏指示器");
            } else {
                binding.bannerViewPager.setAutoPlay(true);
                binding.bannerViewPager.setCanLoop(true);
                binding.bannerViewPager.setInterval(4000);
                binding.bannerViewPager.setIndicatorVisibility(View.VISIBLE); // 多张图片显示指示器
                MyLog.d(TAG, "多张图片，启用自动播放和循环，显示指示器");
            }

            MyLog.d(TAG, "轮播图动态配置完成，图片数量: " + itemCount);
        } catch (Exception e) {
            MyLog.e(TAG, "设置轮播图动态配置失败", e);
        }
    }


    /**
     * 设置轮播图配置 - 参考LocalThemeDetailScene的最新配置
     */
    private void setupBannerView() {
        try {
            binding.bannerViewPager
                    // 基本配置
                    .setAdapter(bannerAdapter)
                    // 移除不存在的方向设置，BannerViewPager默认就是水平方向
                    .setUserInputEnabled(true)
                    .setScrollDuration(400)

                    // 页面样式
                    .setPageStyle(PageStyle.NORMAL)
                    .setRevealWidth(0)
                    .setPageMargin(0)

                    // 指示器配置 - 现代化长条样式
                    .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    .setIndicatorSlideMode(IndicatorSlideMode.WORM)
                    .setIndicatorGravity(IndicatorGravity.CENTER)
                    .setIndicatorSliderWidth(BannerUtils.dp2px(8), BannerUtils.dp2px(24)) // 小点8dp → 长条24dp
                    .setIndicatorSliderGap(BannerUtils.dp2px(4))
                    .setIndicatorSliderRadius(BannerUtils.dp2px(4))
                    .setIndicatorSliderColor(
                            Color.parseColor("#333333"),     // 选中：白色长条
                            Color.parseColor("#4DFFFFFF")    // 未选中：30%透明白色小点
                    )
                    .setIndicatorVisibility(View.VISIBLE)
                    .showIndicatorWhenOneItem(false)
                    .setIndicatorMargin(0, 0, 0, BannerUtils.dp2px(20)) // 底部边距20dp

                    // 轮播行为 - 初始设置为不自动播放
                    .setAutoPlay(false)
                    .setCanLoop(false)
                    .setInterval(4000)
                    .stopLoopWhenDetachedFromWindow(true)

                    // 生命周期 - 使用项目中实际存在的方法
                    .registerLifecycleObserver(getLifecycle())

                    // 点击监听
                    .setOnPageClickListener((view, position) -> {
                        MyLog.d(TAG, "点击轮播图第" + position + "张");
                        onBannerImageClick(position);
                    });
            // 注意：不在这里调用create()，而是在数据加载时调用

            MyLog.d(TAG, "轮播图基本配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "轮播图配置失败", e);
        }
    }

    /**
     * 加载主题轮播图
     */
    private void loadThemeBanner() {
        if (themeInfo == null) return;

        MyLog.d(TAG, "加载主题轮播图数据");

        try {
            java.util.List<ThemeBannerModel.BannerItem> bannerItems = null;

            // 使用统一模型的预览图片
            if (themeInfo.getPreviewImages() != null && !themeInfo.getPreviewImages().isEmpty()) {
                MyLog.d(TAG, "使用预览图片，数量: " + themeInfo.getPreviewImages().size());
                bannerItems = createBannerItemsFromUnifiedModel(themeInfo.getPreviewImages());
            } else if (themeInfo.getCoverImage() != null && !themeInfo.getCoverImage().isEmpty()) {
                // 回退到封面图片
                MyLog.d(TAG, "使用封面图片: " + themeInfo.getCoverImage());
                bannerItems = ThemeBannerModel.createFromThemeInfo(themeInfo.getCoverImage());
            } else {
                MyLog.w(TAG, "没有可用的预览图片");
                bannerItems = new java.util.ArrayList<>();
            }

            if (bannerItems != null && !bannerItems.isEmpty()) {
                currentBannerItemCount = bannerItems.size();

                // 先设置动态配置
                setupBannerBasicConfig(currentBannerItemCount);

                // 使用create(list)方法创建轮播图
                binding.bannerViewPager.create(bannerItems);

                MyLog.d(TAG, "轮播图数据加载完成，共" + bannerItems.size() + "张图片");

                // 添加调试信息
                for (int i = 0; i < bannerItems.size(); i++) {
                    ThemeBannerModel.BannerItem item = bannerItems.get(i);
                    MyLog.d(TAG, "轮播图项 " + i + ": " + item.getTitle() + " - " + item.getImageUrl());
                }

                // 如果是多张图片，直接启动自动播放
                if (currentBannerItemCount > 1) {
                    binding.bannerViewPager.startLoop();
                    MyLog.d(TAG, "自动轮播已启动");
                }
            } else {
                MyLog.w(TAG, "轮播图数据为空，使用默认数据");
                loadDefaultBannerData();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载轮播图数据失败", e);
            loadDefaultBannerData();
        }
    }

    /**
     * 从统一模型预览图片创建轮播图数据
     */
    private java.util.List<ThemeBannerModel.BannerItem> createBannerItemsFromUnifiedModel(java.util.List<UnifiedThemeModel.PreviewImage> previewImages) {
        java.util.List<ThemeBannerModel.BannerItem> bannerItems = new java.util.ArrayList<>();

        // 先按sortOrder排序
        previewImages.sort((a, b) -> {
            int sortA = a.getSortOrder() != null ? a.getSortOrder() : 0;
            int sortB = b.getSortOrder() != null ? b.getSortOrder() : 0;
            return Integer.compare(sortA, sortB);
        });

        // 创建轮播图项
        for (UnifiedThemeModel.PreviewImage previewImage : previewImages) {
            String imageUrl = previewImage.getImageUrl();
            String title = previewImage.getTitle() != null ? previewImage.getTitle() : "主题预览";
            String description = previewImage.getDescription() != null ? previewImage.getDescription() : "";
            int type = previewImage.getImageType() != null ? previewImage.getImageType() : 0;

            bannerItems.add(new ThemeBannerModel.BannerItem(imageUrl, title, description, type));
        }

        return bannerItems;
    }


    /**
     * 从API加载主题详情
     */
    private void loadThemeDetailFromAPI() {
        if (themeListInfo == null) return;

        MyLog.d(TAG, "从API加载主题详情: " + themeListInfo.getId());

        disposables.add(
                RxHttp.get(Const.NEW_THEME_DETAIL + themeListInfo.getId())
                        .setCacheMode(CacheMode.ONLY_NETWORK)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleThemeDetailSuccess,
                                this::handleThemeDetailError
                        )
        );
    }

    /**
     * 处理主题详情获取成功
     */
    private void handleThemeDetailSuccess(String responseString) {
        MyLog.v(TAG, "主题详情API响应: " + responseString);

        try {
            UnifiedThemeResponse response = new Gson().fromJson(responseString, UnifiedThemeResponse.class);

            if (response != null && response.isSuccess() && response.getData() != null) {
                UnifiedThemeModel latestThemeInfo = response.getData();
                MyLog.v(TAG, "主题详情获取成功: " + latestThemeInfo.getThemeName());

                // 更新主题信息
                this.themeInfo = latestThemeInfo;

                // 重新设置UI
                setupThemeInfo();
                // 初始化轮播图
                loadThemeBanner();
                // 重新加载UI数据
                refreshUIWithLatestData();

            } else {
                MyLog.w(TAG, "主题详情API响应异常: " + (response != null ? response.getMsg() : "null"));
                // 使用现有数据继续显示
                loadThemeData();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "解析主题详情响应失败", e);
            // 使用现有数据继续显示
            loadThemeData();
        }
    }

    /**
     * 处理主题详情获取失败
     */
    private void handleThemeDetailError(Throwable throwable) {
        MyLog.e(TAG, "获取主题详情失败", throwable);
        // 使用现有数据继续显示
        loadThemeData();
    }


    /**
     * 使用最新数据刷新UI
     */
    private void refreshUIWithLatestData() {
        // 重新设置基本信息
        setupThemeInfo();

        // 重新加载轮播图
        loadThemeBanner();

        // 更新按钮状态
        updateButtonStates();

        MyLog.d(TAG, "UI已使用最新数据刷新");
    }

    /**
     * 加载默认轮播图数据
     */
    private void loadDefaultBannerData() {
        java.util.List<ThemeBannerModel.BannerItem> defaultItems = new java.util.ArrayList<>();
        defaultItems.add(new ThemeBannerModel.BannerItem(
                "",
                "主题预览",
                "查看主题整体效果",
                0
        ));

        currentBannerItemCount = defaultItems.size();

        // 设置动态配置
        setupBannerBasicConfig(currentBannerItemCount);

        // 创建轮播图
        binding.bannerViewPager.create(defaultItems);

        MyLog.d(TAG, "默认轮播图数据加载完成");
    }

    /**
     * 轮播图点击事件处理 - 简化版本
     */
    private void onBannerImageClick(int position) {
        MyLog.d(TAG, "轮播图点击事件，位置: " + position);

        // 简单的点击反馈
        if (currentBannerItemCount > 1) {
            String hint = "第" + (position + 1) + "张图片，共" + currentBannerItemCount + "张";
            if (position == 0) {
                hint += " - 卡片模式";
            } else if (position == 1) {
                hint += " - 地图模式";
            }
            MToast.makeTextShort(hint);
        } else {
            MToast.makeTextShort("主题预览图");
        }
    }


    /**
     * 显示成功消息
     */
    private void showSuccessMessage(String message) {
        MToast.makeTextShort(message);

        // 添加成功反馈动画
        if (binding != null) {
            // 可以添加一些成功的视觉反馈，比如轻微的震动或者颜色变化
            MyLog.d(TAG, "操作成功: " + message);
        }
    }

    /**
     * 更新按钮状态
     */
    private void updateButtonStates() {
        if (actionBar == null) return;

        // 根据操作状态禁用/启用按钮
        boolean isCurrentTheme = themeInfo.getIsCurrentTheme() != null && themeInfo.getIsCurrentTheme();
        actionBar.setPrimaryActionEnabled(!isOperationInProgress && !isCurrentTheme);
        actionBar.setSecondaryActionEnabled(!isOperationInProgress && !isDownloading);

        // 更新按钮文本和进度显示
        if (isOperationInProgress) {
            if (isDownloading) {
                // 显示下载进度
                actionBar.showProgress(0, "准备下载...");
            }
        } else {
            // 恢复正常状态
            actionBar.hideProgress();
            updateActionBar();
        }
    }

    /**
     * 更新统一操作栏
     */
    private void updateActionBar() {
        if (themeInfo == null || actionBar == null) return;

        boolean isCurrentTheme = themeInfo.getIsCurrentTheme() != null && themeInfo.getIsCurrentTheme();
        boolean isDownloaded = themeInfo.getIsDownloaded() != null && themeInfo.getIsDownloaded();

        if (isDownloaded) {
            // 已下载的主题，只能应用
            String buttonText = isCurrentTheme ? "当前使用" : "应用主题";
            actionBar.setPrimaryAction(buttonText, this::onApplyClick);
            actionBar.setPrimaryActionEnabled(!isCurrentTheme);
            actionBar.hideSecondaryAction();

        } else {
            // 未下载的主题，显示应用和下载按钮
            actionBar.setPrimaryAction("应用主题", this::onApplyClick);
            actionBar.setPrimaryActionEnabled(false); // 未下载时不能应用

            actionBar.setSecondaryAction("下载主题", this::onDownloadClick);
            actionBar.setSecondaryActionEnabled(!isDownloading && !isOperationInProgress);
        }
    }

    /**
     * 检查主题是否已下载
     */
    private boolean isThemeDownloaded() {
        // 首先检查数据库标记
        if (themeInfo.getIsDownloaded() != null && themeInfo.getIsDownloaded()) {
            return true;
        }

        // 检查本地文件是否存在
        String localFilePath = themeInfo.getLocalFilePath();
        if (localFilePath != null && !localFilePath.isEmpty()) {
            return FileUtils.isExist(new File(localFilePath));
        }

        // 如果有主题包信息，检查默认路径
        if (themeInfo.getThemePackage() != null && themeInfo.getThemePackage().getFileName() != null) {
            String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
            String fileName = themeInfo.getThemePackage().getFileName();
            String localPath = absolutePath + "/skin/" + fileName;
            return FileUtils.isExist(new File(localPath));
        }

        return false;
    }


    /**
     * 获取下载链接
     */
    private String getDownloadUrl() {
        if (themeInfo == null || themeInfo.getThemePackage() == null) return null;

        // 优先使用downloadUrl
        String downloadUrl = themeInfo.getThemePackage().getDownloadUrl();
        if (downloadUrl != null && !downloadUrl.isEmpty()) {
            return downloadUrl;
        }

        // 回退到packagePath
        return themeInfo.getThemePackage().getPackagePath();
    }

    /**
     * 获取主题文件名
     */
    private String getThemeFileName() {
        if (themeInfo == null || themeInfo.getThemePackage() == null) return null;
        return themeInfo.getThemePackage().getFileName();
    }

    /**
     * 应用按钮点击事件
     */
    private void onApplyClick() {
        if (!checkClickDebounce()) return;

        MyLog.d(TAG, "应用主题: " + themeInfo.getThemeName());

        // 显示确认对话框
        showApplyConfirmDialog();
    }


    /**
     * 下载按钮点击事件
     */
    private void onDownloadClick() {
        if (!checkClickDebounce() || isDownloading || isOperationInProgress) return;

        MyLog.d(TAG, "下载主题: " + themeInfo.getThemeName());

        downloadTheme();
    }


    /**
     * 检查点击防抖
     */
    private boolean checkClickDebounce() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME) {
            MyLog.d(TAG, "点击过于频繁，忽略本次点击");
            return false;
        }
        lastClickTime = currentTime;
        return true;
    }


    /**
     * 显示应用确认对话框
     */
    private void showApplyConfirmDialog() {
        boolean isCurrentTheme = themeInfo.getIsCurrentTheme() != null && themeInfo.getIsCurrentTheme();
        if (isCurrentTheme) {
            MToast.makeTextShort("该主题已经在使用中");
            return;
        }

        String message = "确定要应用主题「" + themeInfo.getThemeName() + "」吗？";

        // 检查是否需要切换模式
        Integer themeType = themeInfo.getThemeType();
        if (themeType != null) {
            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            // 根据API文档：0=白天，1=夜间
            if ((themeType == 0 && isNightMode) || (themeType == 1 && !isNightMode)) {
                String modeText = themeType == 0 ? "白天模式" : "夜间模式";
                message += "\n\n应用后将自动切换到" + modeText;
            }
        }

        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认应用主题: " + message);
        applyTheme();
    }

    /**
     * 应用主题
     */
    private void applyTheme() {
        if (isOperationInProgress) return;

        isOperationInProgress = true;
        updateButtonStates();

        try {
            Integer themeType = themeInfo.getThemeType();
            if (themeType == null) {
                throw new RuntimeException("主题类型未知");
            }

            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            // 根据API文档：0=白天，1=夜间
            String fileName = getThemeFileName();
            if (fileName == null) {
                throw new RuntimeException("无法获取主题文件名");
            }

            if (themeType == 0) {
                // 白天主题
                registerAndLoadSkin();
                SettingsManager.setDayTheme(fileName);
                if (isNightMode) {
                    SettingsManager.setThemeMode(1); // 切换到白天模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用白天主题: " + fileName);
            } else if (themeType == 1) {
                // 夜间主题
                registerAndLoadSkin();
                SettingsManager.setNightTheme(fileName);
                if (!isNightMode) {
                    SettingsManager.setThemeMode(2); // 切换到夜间模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用夜间主题: " + fileName);
            }
        } catch (Exception e) {
            MyLog.e(TAG, "应用主题失败", e);
            MToast.makeTextShort("应用主题失败，请重试");
        } finally {
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    /**
     * 注册并加载皮肤
     */
    private void registerAndLoadSkin() {
        String fileName = getThemeFileName();
        String localFilePath = themeInfo.getLocalFilePath();

        if (localFilePath != null && !localFilePath.isEmpty() && fileName != null) {
            // 使用本地文件路径
            SkinManager.getInstance().registerFileSkin(localFilePath);
            SkinManager.getInstance().loadSkin(fileName);
        } else if (fileName != null) {
            // 使用默认路径
            String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
            String skinPath = absolutePath + "/skin/" + fileName;
            SkinManager.getInstance().registerFileSkin(skinPath);
            SkinManager.getInstance().loadSkin(fileName);
        } else {
            // 恢复默认主题
            SkinManager.getInstance().restoreDefaultTheme();
        }

        MyLog.v(TAG, "应用主题: " + (fileName != null ? fileName : "默认主题"));
    }

    /**
     * 更新主题使用状态
     */
    private void updateThemeUsageStatus() {
        // 设置主题为当前使用
        disposables.add(
                dbManager.setCurrentTheme(themeInfo.getId())
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "主题使用状态更新成功");
                                    updateActionBar();
                                },
                                throwable -> MyLog.e(TAG, "主题使用状态更新失败", throwable)
                        )
        );
    }


    /**
     * 下载主题
     */
    private void downloadTheme() {
        if (isOperationInProgress || !NetworkUtils.isNetworkConnected(requireSceneContext())) {
            if (!NetworkUtils.isNetworkConnected(requireSceneContext())) {
                MToast.makeTextShort("网络连接不可用，请检查网络设置");
            }
            return;
        }

        isOperationInProgress = true;
        isDownloading = true;
        updateButtonStates();

        String fileName = themeInfo.getThemePackage().getFileName();
        MyLog.d(TAG, "开始下载主题: " + (fileName != null ? fileName : "未知"));

        // 更新下载统计
        updateDownloadStats();

        // 开始真实下载（主题包信息已经在详情API中获取）
        downloadTimeMillis = String.valueOf(System.currentTimeMillis());
        startRealDownload();
    }

    /**
     * 更新下载统计
     */
    private void updateDownloadStats() {
        MyLog.d(TAG, "更新下载统计: " + themeInfo.getThemeName());

        // 使用新API记录下载统计
        updateDownloadStatsNewAPI();
    }

    /**
     * 使用新API更新下载统计
     */
    private void updateDownloadStatsNewAPI() {
        String requestBody = "{\n" +
                "  \"packageId\": null,\n" +
                "  \"userId\": null,\n" +
                "  \"deviceId\": \"" + android.provider.Settings.Secure.getString(
                requireSceneContext().getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID) + "\",\n" +
                "  \"appVersion\": \"" + com.smartcar.easylauncher.BuildConfig.VERSION_NAME + "\",\n" +
                "  \"platform\": \"android\",\n" +
                "  \"downloadSource\": \"theme_detail\"\n" +
                "}";

        disposables.add(
                RxHttp.postJson(Const.NEW_THEME_DOWNLOAD + themeInfo.getId())
                        .addAll(requestBody)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                responseString -> {
                                    try {
                                        Gson gson = new Gson();
                                        TypeToken<ApiResponse<Object>> typeToken = new TypeToken<ApiResponse<Object>>() {
                                        };
                                        ApiResponse<Object> response = gson.fromJson(responseString, typeToken.getType());

                                        if (response != null && response.isSuccess()) {
                                            MyLog.v(TAG, "新API下载统计更新成功: " + response.getMsg());
                                            // 更新本地显示的下载量
                                            Long currentCount = themeInfo.getDownloadCount() != null ? themeInfo.getDownloadCount() : 0L;
                                            themeInfo.setDownloadCount(currentCount + 1);
                                        } else {
                                            MyLog.w(TAG, "新API下载统计响应异常: " + (response != null ? response.getMsg() : "null"));
                                        }
                                    } catch (Exception e) {
                                        MyLog.e(TAG, "解析下载统计响应失败", e);
                                    }
                                },
                                throwable -> {
                                    MyLog.e(TAG, "新API下载统计更新失败", throwable);
                                    // 静默失败，不影响下载流程
                                }
                        )
        );
    }


    /**
     * 开始真实下载
     */
    private void startRealDownload() {
        String fileName = themeInfo.getThemePackage().getFileName();
        String downloadUrl = themeInfo.getThemePackage().getDownloadUrl();

        if (fileName == null || downloadUrl == null) {
            onDownloadError(new RuntimeException("无法获取主题文件信息"));
            return;
        }

        // 文件存储路径
        String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
        String destPath = absolutePath + "/skin/" + fileName;

        MyLog.d(TAG, "下载路径: " + destPath);

        downloadDisposable = RxHttp.get(downloadUrl)
                .tag(downloadTimeMillis)
                .toDownloadObservable(destPath)
                .onMainProgress(progress -> {
                    // 下载进度回调
                    int currentProgress = progress.getProgress(); // 当前进度 0-100
                    long currentSize = progress.getCurrentSize(); // 当前已下载的字节大小
                    long totalSize = progress.getTotalSize(); // 要下载的总字节大小

                    MyLog.v(TAG, "下载进度: " + currentProgress + "%");

                    // 更新进度显示
                    if (actionBar != null) {
                        actionBar.showProgress(currentProgress, "下载中...");
                    }
                })
                .subscribe(
                        downloadPath -> {
                            // 下载完成
                            MyLog.d(TAG, "下载完成: " + downloadPath);
                            onRealDownloadComplete(downloadPath);
                        },
                        throwable -> {
                            // 下载失败
                            MyLog.e(TAG, "下载失败", throwable);
                            onDownloadError(throwable);
                        }
                );

        // 添加到disposables中管理
        disposables.add(downloadDisposable);
    }


    /**
     * 真实下载完成处理
     */
    private void onRealDownloadComplete(String downloadPath) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        // 更新主题下载状态
        themeInfo.setIsDownloaded(true);
        themeInfo.setLocalFilePath(downloadPath);
        themeInfo.setDownloadTime(String.valueOf(System.currentTimeMillis()));

        // 打印详细的主题信息用于调试
        MyLog.d(TAG, "准备保存主题到数据库:");
        MyLog.d(TAG, "  - ID: " + themeInfo.getId());
        MyLog.d(TAG, "  - Name: " + themeInfo.getThemeName());
        MyLog.d(TAG, "  - ThemeType: " + themeInfo.getThemeType());
        MyLog.d(TAG, "  - DownloadCount: " + themeInfo.getDownloadCount());

        // 保存主题到新数据库
        disposables.add(
                dbManager.saveTheme(themeInfo)
                        .subscribe(
                                result -> {
                                    MyLog.v(TAG, "主题保存到数据库成功，ID: " + result);
                                    showSuccessMessage("主题下载完成，可在「我的主题」中查看");
                                    updateActionBar();
                                },
                                throwable -> {
                                    MyLog.e(TAG, "主题保存到数据库失败", throwable);
                                    showErrorMessage("主题下载完成，但保存失败");
                                    updateActionBar();
                                }
                        )
        );
    }


    /**
     * 下载错误处理
     */
    private void onDownloadError(Throwable throwable) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        String errorMessage = "下载失败";
        if (throwable != null) {
            errorMessage += ": " + throwable.getMessage();
        }

        MyLog.e(TAG, errorMessage, throwable);
        showErrorMessage(errorMessage);

        // 更新操作栏状态
        updateActionBar();
    }


    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        MToast.makeTextShort(message);
        MyLog.e(TAG, "错误: " + message);
    }


    @Override
    public void onResume() {
        super.onResume();

        // 恢复轮播图自动播放
        if (currentBannerItemCount > 1) {
            binding.bannerViewPager.startLoop();
            MyLog.d(TAG, "页面恢复，自动轮播已启动");
        }

        // 重置操作状态（防止异常情况下状态未正确恢复）
        if (isOperationInProgress && !isDownloading) {
            MyLog.w(TAG, "检测到异常操作状态，重置状态");
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        // 暂停轮播图自动播放
        binding.bannerViewPager.stopLoop();
        MyLog.d(TAG, "页面暂停，自动轮播已停止");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 取消下载
        if (downloadDisposable != null && !downloadDisposable.isDisposed()) {
            downloadDisposable.dispose();
        }

        // 停止轮播图
        binding.bannerViewPager.stopLoop();

        // 清理资源
        disposables.clear();
        bannerAdapter = null;
        actionBar = null;
        binding = null;
    }


}
