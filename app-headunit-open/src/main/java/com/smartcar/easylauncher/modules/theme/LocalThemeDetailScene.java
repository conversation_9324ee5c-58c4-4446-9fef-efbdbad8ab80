package com.smartcar.easylauncher.modules.theme;

import static com.bytedance.scene.navigation.NavigationSceneGetter.getNavigationScene;

import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.ViewCompat;


import com.google.gson.Gson;
import com.maning.mndialoglibrary.MToast;
import com.smartcar.easylauncher.shared.adapter.theme.ThemeBannerAdapter;
import com.smartcar.easylauncher.core.base.BaseScene;
import com.smartcar.easylauncher.databinding.SceneLocalThemeDetailBinding;
import com.smartcar.easylauncher.data.database.dbmager.DbManager;
import com.smartcar.easylauncher.data.database.entity.ThemeInfoModel;
import com.smartcar.easylauncher.core.manager.SettingsManager;
import com.smartcar.easylauncher.core.constants.SettingsConstants;
import com.smartcar.easylauncher.data.model.theme.ThemeBannerModel;
import com.smartcar.easylauncher.data.model.theme.ThemeListModel;
import com.smartcar.easylauncher.data.model.theme.api.ApiResponse;
import com.google.gson.reflect.TypeToken;
import com.smartcar.easylauncher.infrastructure.skin.SkinManager;
import com.smartcar.easylauncher.shared.utils.MyLog;
import com.smartcar.easylauncher.shared.utils.NetworkUtils;
import com.smartcar.easylauncher.shared.utils.file.FileUtils;
import com.smartcar.easylauncher.core.constants.Const;
import com.smartcar.easylauncher.shared.view.SecondaryPageActionBar;
import com.zhpan.bannerview.constants.IndicatorGravity;
import com.zhpan.bannerview.constants.PageStyle;
import com.zhpan.bannerview.utils.BannerUtils;

import com.zhpan.indicator.enums.IndicatorSlideMode;
import com.zhpan.indicator.enums.IndicatorStyle;

import android.annotation.SuppressLint;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.disposables.Disposable;
import rxhttp.RxHttp;


/**
 * 主题详情Scene
 * 显示主题的详细信息，支持预览、下载、应用和删除操作
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
public class LocalThemeDetailScene extends BaseScene {

    private static final String TAG = "LocalThemeDetailScene";
    private static final String KEY_THEME_INFO = "theme_info";

    // 共享元素动画常量
    public static final String VIEW_NAME_THEME_IMAGE = "theme:detail:image";
    public static final String VIEW_NAME_THEME_TITLE = "theme:detail:title";
    
    private SceneLocalThemeDetailBinding binding;
    private ThemeInfoModel themeInfo;
    private final CompositeDisposable disposables = new CompositeDisposable();
    private DbManager dbManager;
    private boolean isDownloading = false;

    // 统一操作栏组件
    private SecondaryPageActionBar actionBar;

    // 轮播图相关
    private ThemeBannerAdapter bannerAdapter;
    private int currentBannerItemCount = 0;

    // 操作状态管理
    private boolean isOperationInProgress = false;
    private long lastClickTime = 0;
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1秒防抖

    // 下载相关
    private Disposable downloadDisposable;
    private String downloadTimeMillis;

    // 版本更新相关
    private ThemeInfoModel latestServerTheme;
    private boolean isCheckingUpdate = false;

    /**
     * 创建主题详情Scene实例
     */
    public static LocalThemeDetailScene newInstance(ThemeInfoModel themeInfo) {
        LocalThemeDetailScene scene = new LocalThemeDetailScene();
        Bundle args = new Bundle();
        args.putString(KEY_THEME_INFO, new Gson().toJson(themeInfo));
        scene.setArguments(args);
        return scene;
    }

    @NonNull
    @Override
    public ViewGroup onCreateNewView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = SceneLocalThemeDetailBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initData();
        initViews();
        setupSharedElements();
        loadThemeData();

        // 检查主题更新（仅对非默认主题）
        if (themeInfo != null && themeInfo.getClassify() != 0 &&
            NetworkUtils.isNetworkConnected(requireSceneContext())) {
            checkThemeUpdate();
        }
    }

    /**
     * 设置共享元素动画
     */
    private void setupSharedElements() {
        if (themeInfo != null) {
            // 设置轮播图的共享元素名称
            ViewCompat.setTransitionName(binding.bannerViewPager, VIEW_NAME_THEME_IMAGE + themeInfo.getId());
            // 设置主题名称的共享元素名称
            ViewCompat.setTransitionName(binding.tvThemeName, VIEW_NAME_THEME_TITLE + themeInfo.getId());
        }
    }

    /**
     * 初始化数据
     */
    private void initData() {
        dbManager = DbManager.getInstance();
        
        // 从参数中获取主题信息
        Bundle args = getArguments();
        if (args != null) {
            String themeJson = args.getString(KEY_THEME_INFO);
            if (themeJson != null) {
                try {
                    themeInfo = new Gson().fromJson(themeJson, ThemeInfoModel.class);
                } catch (Exception e) {
                    MyLog.e(TAG, "解析主题信息失败", e);
                }
            }
        }
        
        if (themeInfo == null) {
            MyLog.e(TAG, "主题信息为空，返回上一页");
            getNavigationScene(this).pop();
        }
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        // 初始化统一操作栏
        initActionBar();

        // 初始化轮播图
        initBannerView();
    }

    /**
     * 初始化统一操作栏
     */
    private void initActionBar() {
        actionBar = binding.actionBar;

        // 设置返回按钮
        actionBar.setBackAction(() -> getNavigationScene(this).pop());

        // 根据主题状态设置操作按钮
        updateActionBar();
    }

    /**
     * 加载主题数据
     */
    private void loadThemeData() {
        if (themeInfo == null) return;

        // 设置页面标题
        //binding.tvTitle.setText("主题详情");
        
        // 设置主题基本信息
        binding.tvThemeName.setText(themeInfo.getName());
        binding.tvThemeAuthor.setText("作者: " + themeInfo.getAuthor());
        binding.tvThemeDescription.setText(themeInfo.getContent());
        binding.tvThemeVersion.setText("版本: " + themeInfo.getVersionName());
        // 根据主题类型显示不同的信息
        if (themeInfo.getClassify() == 0) {
            // 默认主题显示"系统内置"
            binding.tvDownloadCount.setText("内置主题");
        } else {
            // 用户主题显示下载量
            binding.tvDownloadCount.setText("下载量: " + themeInfo.getNumber());
        }
        
        // 设置主题类型
        String themeType = themeInfo.getThemeType() == 0 ? "夜间主题" : "白天主题";
        binding.tvThemeType.setText(themeType);
        
        // 设置主题标签
        if (themeInfo.getLabel() != null && !themeInfo.getLabel().trim().isEmpty()) {
            binding.tvThemeLabel.setText(themeInfo.getLabel());
            binding.tvThemeLabel.setVisibility(View.VISIBLE);
        } else {
            binding.tvThemeLabel.setVisibility(View.GONE);
        }

        // 调试日志 - 检查更新相关字段
        MyLog.d(TAG, "主题更新信息调试:");
        MyLog.d(TAG, "- isUpdate(): " + themeInfo.isUpdate());
        MyLog.d(TAG, "- getVersionName(): " + themeInfo.getVersionName());
        MyLog.d(TAG, "- getUpdateContent(): " + themeInfo.getUpdateContent());

        // 设置更新提示 - 参考弹窗的逻辑
        if (themeInfo.isUpdate()) {
            binding.tvUpdateHint.setText("有新版本");
            binding.tvUpdateHint.setVisibility(View.VISIBLE);
        } else {
            binding.tvUpdateHint.setVisibility(View.GONE);
        }

        // 显示详细更新信息（独立于isUpdate判断）
        showUpdateInfo();
        
        // 加载主题轮播图
        loadThemeBanner();

        // 更新操作按钮
        updateActionBar();

        // 已改为普通轮播样式，不再显示一屏多页功能提示
    }

    /**
     * 显示更新信息 - 优化版本，区分有更新和无更新的情况
     */
    private void showUpdateInfo() {
        if (themeInfo == null) {
            binding.updateInfoContainer.setVisibility(View.GONE);
            return;
        }

        // 检查是否有更新内容需要显示
        boolean hasUpdateContent = (themeInfo.getUpdateContent() != null && !themeInfo.getUpdateContent().trim().isEmpty()) ||
                                  (themeInfo.getVersionName() != null && !themeInfo.getVersionName().trim().isEmpty());

        if (!hasUpdateContent) {
            binding.updateInfoContainer.setVisibility(View.GONE);
            return;
        }

        binding.updateInfoContainer.setVisibility(View.VISIBLE);

        // 根据是否有更新设置不同的标题和版本文案
        String titleText;
        String versionText;

        if (themeInfo.isUpdate()) {
            // 有更新：显示"新版本可用"标题
            titleText = "新版本可用";
            if (themeInfo.getVersionName() != null && !themeInfo.getVersionName().trim().isEmpty()) {
                versionText = "版本 v" + themeInfo.getVersionName();
            } else {
                versionText = "有新版本可用";
            }
        } else {
            // 无更新：显示"版本信息"标题
            titleText = "版本信息";
            if (themeInfo.getVersionName() != null && !themeInfo.getVersionName().trim().isEmpty()) {
                versionText = "当前版本 v" + themeInfo.getVersionName();
            } else {
                versionText = "更新日志";
            }
        }

        // 设置标题和版本文本
        binding.tvUpdateTitle.setText(titleText);
        binding.tvUpdateVersion.setText(versionText);

        // 控制NEW标签的显示
        if (themeInfo.isUpdate()) {
            binding.tvUpdateBadge.setVisibility(View.VISIBLE);
            binding.tvUpdateBadge.setText("NEW");
        } else {
            binding.tvUpdateBadge.setVisibility(View.GONE);
        }

        // 设置更新内容
        String updateContent = themeInfo.getUpdateContent();
        if (updateContent != null && !updateContent.trim().isEmpty()) {
            // 将更新内容格式化为美观的列表
            String formattedContent = formatUpdateContent(updateContent);
            binding.tvUpdateContent.setText(formattedContent);
        } else {
            // 如果没有具体更新内容，根据是否有更新显示不同的默认内容
            if (themeInfo.isUpdate()) {
                binding.tvUpdateContent.setText("• 性能优化和体验提升\n• 修复已知问题\n• 增强系统稳定性");
            } else {
                binding.tvUpdateContent.setText("• 当前已是最新版本\n• 感谢您的使用");
            }
        }

        MyLog.d(TAG, "显示更新信息 - isUpdate: " + themeInfo.isUpdate() +
                ", 版本: " + themeInfo.getVersionName() +
                ", 内容: " + themeInfo.getUpdateContent());
    }

    /**
     * 格式化更新内容为美观的列表形式
     */
    private String formatUpdateContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }

        // 如果内容已经包含项目符号，直接返回
        if (content.contains("•") || content.contains("·") || content.contains("-")) {
            return content;
        }

        // 按行分割并添加项目符号
        String[] lines = content.split("\n");
        StringBuilder formatted = new StringBuilder();

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();
            if (!line.isEmpty()) {
                // 移除可能存在的数字序号
                line = line.replaceFirst("^\\d+\\.\\s*", "");
                formatted.append("• ").append(line);
                if (i < lines.length - 1) {
                    formatted.append("\n");
                }
            }
        }

        return formatted.toString();
    }

    /**
     * 初始化轮播图
     */
    private void initBannerView() {
        bannerAdapter = new ThemeBannerAdapter();

        // 设置轮播图配置
        setupBannerView();
    }

    /**
     * 设置轮播图动态配置 - 根据图片数量
     */
    private void setupBannerBasicConfig(int itemCount) {

        try {
            // 根据图片数量设置自动播放和循环
            if (itemCount <= 1) {
                binding.bannerViewPager.setAutoPlay(false);
                binding.bannerViewPager.setCanLoop(false);
                binding.bannerViewPager.setIndicatorVisibility(View.GONE); // 单张图片隐藏指示器
                MyLog.d(TAG, "单张图片，禁用自动播放和循环，隐藏指示器");
            } else {
                binding.bannerViewPager.setAutoPlay(true);
                binding.bannerViewPager.setCanLoop(true);
                binding.bannerViewPager.setInterval(4000);
                binding.bannerViewPager.setIndicatorVisibility(View.VISIBLE); // 多张图片显示指示器
                MyLog.d(TAG, "多张图片，启用自动播放和循环，显示指示器");
            }

            MyLog.d(TAG, "轮播图动态配置完成，图片数量: " + itemCount);
        } catch (Exception e) {
            MyLog.e(TAG, "设置轮播图动态配置失败", e);
        }
    }



    /**
     * 设置轮播图配置 - 根据官方API优化
     */
    private void setupBannerView() {
        try {
            binding.bannerViewPager
                    // 基本配置
                    .setAdapter(bannerAdapter)
                    // 移除不存在的方向设置，binding.bannerViewPager默认就是水平方向
                    .setUserInputEnabled(true)
                    .setScrollDuration(400)

                    // 页面样式
                    .setPageStyle(PageStyle.NORMAL)
                    .setRevealWidth(0)
                    .setPageMargin(0)

                    // 指示器配置 - 现代化长条样式
                    .setIndicatorStyle(IndicatorStyle.ROUND_RECT)
                    .setIndicatorSlideMode(IndicatorSlideMode.WORM)
                    .setIndicatorGravity(IndicatorGravity.CENTER)
                    .setIndicatorSliderWidth(BannerUtils.dp2px(8), BannerUtils.dp2px(24)) // 普通8dp，选中24dp长条
                    .setIndicatorSliderGap(BannerUtils.dp2px(4))
                    .setIndicatorSliderRadius(BannerUtils.dp2px(4))
                    .setIndicatorSliderColor(
                        Color.parseColor("#333333"),     // 选中：白色长条
                        Color.parseColor("#4DFFFFFF")    // 未选中：30%透明白色小点
                    )
                    .setIndicatorVisibility(View.VISIBLE)
                    .showIndicatorWhenOneItem(false)
                    .setIndicatorMargin(0, 0, 0, BannerUtils.dp2px(20)) // 底部边距20dp

                    // 轮播行为 - 初始设置为不自动播放
                    .setAutoPlay(false)
                    .setCanLoop(false)
                    .setInterval(4000)
                    .stopLoopWhenDetachedFromWindow(true)

                    // 生命周期 - 使用项目中实际存在的方法
                    .registerLifecycleObserver(getLifecycle())

                    // 点击监听
                    .setOnPageClickListener((view, position) -> {
                        MyLog.d(TAG, "点击轮播图第" + position + "张");
                        onBannerImageClick(position);
                    });

            MyLog.d(TAG, "轮播图基本配置完成");
        } catch (Exception e) {
            MyLog.e(TAG, "轮播图配置失败", e);
        }
    }





    /**
     * 加载主题轮播图 - 简化版本
     */
    private void loadThemeBanner() {
        if (themeInfo == null) return;

        MyLog.d(TAG, "加载主题轮播图数据: " + themeInfo.getImg());

        try {
            // 从主题信息创建轮播图数据
            java.util.List<ThemeBannerModel.BannerItem> bannerItems =
                ThemeBannerModel.createFromThemeInfo(themeInfo.getImg());

            if (bannerItems != null && !bannerItems.isEmpty()) {
                currentBannerItemCount = bannerItems.size();

                // 先设置动态配置
                setupBannerBasicConfig(currentBannerItemCount);

                // 使用create(list)方法创建轮播图
                binding.bannerViewPager.create(bannerItems);

                MyLog.d(TAG, "轮播图数据加载完成，共" + bannerItems.size() + "张图片");

                // 添加调试信息
                for (int i = 0; i < bannerItems.size(); i++) {
                    ThemeBannerModel.BannerItem item = bannerItems.get(i);
                    MyLog.d(TAG, "轮播图项 " + i + ": " + item.getTitle() + " - " + item.getImageUrl());
                }

                // 如果是多张图片，直接启动自动播放
                if (currentBannerItemCount > 1) {
                    binding.bannerViewPager.startLoop();
                    MyLog.d(TAG, "自动轮播已启动");
                }
            } else {
                MyLog.w(TAG, "轮播图数据为空，使用默认数据");
                loadDefaultBannerData();
            }
        } catch (Exception e) {
            MyLog.e(TAG, "加载轮播图数据失败", e);
            loadDefaultBannerData();
        }
    }

    /**
     * 加载默认轮播图数据
     */
    private void loadDefaultBannerData() {
        java.util.List<ThemeBannerModel.BannerItem> defaultItems = new java.util.ArrayList<>();
        defaultItems.add(new ThemeBannerModel.BannerItem(
            "",
            "主题预览",
            "查看主题整体效果",
            0
        ));

        currentBannerItemCount = defaultItems.size();

        // 设置动态配置
        setupBannerBasicConfig(currentBannerItemCount);

        // 创建轮播图
        binding.bannerViewPager.create(defaultItems);

        MyLog.d(TAG, "默认轮播图数据加载完成");
    }

    /**
     * 轮播图点击事件处理 - 简化版本
     */
    private void onBannerImageClick(int position) {
        MyLog.d(TAG, "轮播图点击事件，位置: " + position);

        // 简单的点击反馈
        if (currentBannerItemCount > 1) {
            String hint = "第" + (position + 1) + "张图片，共" + currentBannerItemCount + "张";
            if (position == 0) {
                hint += " - 卡片模式";
            } else if (position == 1) {
                hint += " - 地图模式";
            }
            MToast.makeTextShort(hint);
        } else {
            MToast.makeTextShort("主题预览图");
        }
    }



    /**
     * 显示成功消息
     */
    private void showSuccessMessage(String message) {
        MToast.makeTextShort(message);

        // 添加成功反馈动画
        if (binding != null) {
            // 可以添加一些成功的视觉反馈，比如轻微的震动或者颜色变化
            MyLog.d(TAG, "操作成功: " + message);
        }
    }

    /**
     * 更新按钮状态
     */
    private void updateButtonStates() {
        if (actionBar == null) return;

        // 根据操作状态禁用/启用按钮
        actionBar.setPrimaryActionEnabled(!isOperationInProgress && !isThemeCurrentlyInUse());
        actionBar.setSecondaryActionEnabled(!isOperationInProgress && !isDownloading);

        // 更新按钮文本和进度显示
        if (isOperationInProgress) {
            if (isDownloading) {
                // 显示下载进度
                actionBar.showProgress(0, "准备下载...");
            }
        } else {
            // 恢复正常状态
            actionBar.hideProgress();
            updateActionBar();
        }
    }

    /**
     * 更新统一操作栏 - 本地主题支持删除功能
     */
    private void updateActionBar() {
        if (themeInfo == null || actionBar == null) return;

        // 正确判断主题是否正在使用
        boolean isCurrentlyInUse = isThemeCurrentlyInUse();

        // 根据主题状态显示不同的按钮
        if (themeInfo.getClassify() == 0) {
            // 默认主题，只能应用，不能删除
            String buttonText = isCurrentlyInUse ? "当前使用" : "应用主题";
            actionBar.setPrimaryAction(buttonText, this::onApplyClick);
            actionBar.setPrimaryActionEnabled(!isCurrentlyInUse);
            actionBar.hideSecondaryAction();

        } else {
            // 用户下载的主题，可以应用、更新和删除
            String primaryButtonText;
            if (isCurrentlyInUse) {
                primaryButtonText = "当前使用";
            } else if (themeInfo.isUpdate()) {
                primaryButtonText = "更新主题";
            } else {
                primaryButtonText = "应用主题";
            }

            actionBar.setPrimaryAction(primaryButtonText, () -> {
                if (themeInfo.isUpdate() && !isCurrentlyInUse) {
                    onUpdateClick();
                } else {
                    onApplyClick();
                }
            });
            actionBar.setPrimaryActionEnabled(!isCurrentlyInUse);

            // 添加删除按钮
            actionBar.setSecondaryAction("删除主题", this::onDeleteClick);
            actionBar.setSecondaryActionEnabled(!isOperationInProgress);
        }
    }


    /**
     * 应用按钮点击事件
     */
    private void onApplyClick() {
        if (!checkClickDebounce()) return;

        MyLog.d(TAG, "应用主题: " + themeInfo.getName());

        // 显示确认对话框
        showApplyConfirmDialog();
    }





    /**
     * 更新按钮点击事件
     */
    private void onUpdateClick() {
        if (!checkClickDebounce() || isDownloading || isOperationInProgress) return;

        MyLog.d(TAG, "更新主题: " + themeInfo.getName());

        // 显示更新确认对话框
        showUpdateConfirmDialog();
    }

    /**
     * 删除按钮点击事件
     */
    private void onDeleteClick() {
        if (!checkClickDebounce() || isOperationInProgress) return;

        MyLog.d(TAG, "删除主题: " + themeInfo.getName());

        // 显示删除确认对话框
        showDeleteConfirmDialog();
    }

    /**
     * 正确判断主题是否正在使用
     * 通过比较当前设置的主题名称来判断，而不是依赖数据库中的isUse字段
     */
    private boolean isThemeCurrentlyInUse() {
        if (themeInfo == null) return false;

        // 获取当前设置的主题
        String currentDayTheme = SettingsManager.getDayTheme();
        String currentNightTheme = SettingsManager.getNightTheme();

        // 构建主题文件名
        String themeFileName = themeInfo.getName() + ".skin";

        MyLog.d(TAG, "判断主题使用状态:");
        MyLog.d(TAG, "  - 主题名称: " + themeInfo.getName());
        MyLog.d(TAG, "  - 主题类型: " + themeInfo.getThemeType() + " (0=夜间, 1=白天)");
        MyLog.d(TAG, "  - 主题分类: " + themeInfo.getClassify() + " (0=默认, 1=用户)");
        MyLog.d(TAG, "  - 当前白天主题: " + currentDayTheme);
        MyLog.d(TAG, "  - 当前夜间主题: " + currentNightTheme);
        MyLog.d(TAG, "  - 主题文件名: " + themeFileName);

        boolean isInUse = false;

        // 对于默认主题的特殊处理 - 根据API文档：0=白天，1=夜间
        if (themeInfo.getClassify() == 0) {
            if (themeInfo.getThemeType() == 0) {
                // 默认白天主题：检查白天主题设置是否为空
                isInUse = currentDayTheme.isEmpty() ||
                         currentDayTheme.contains("默认白天主题");
                MyLog.d(TAG, "  - 默认白天主题判断结果: " + isInUse);
            } else {
                // 默认夜间主题：检查夜间主题设置是否为空或为默认值
                isInUse = currentNightTheme.isEmpty() ||
                         currentNightTheme.equals(SettingsConstants.DEFAULT_NIGHT_THEME) ||
                         currentNightTheme.contains("默认夜间主题");
                MyLog.d(TAG, "  - 默认夜间主题判断结果: " + isInUse);
            }
        } else {
            // 用户下载的主题：检查主题文件名是否匹配
            if (themeInfo.getThemeType() == 0) {
                // 夜间主题
                isInUse = currentNightTheme.equals(themeFileName) ||
                         currentNightTheme.contains(themeInfo.getName());
                MyLog.d(TAG, "  - 用户夜间主题判断结果: " + isInUse);
            } else {
                // 白天主题
                isInUse = currentDayTheme.equals(themeFileName) ||
                         currentDayTheme.contains(themeInfo.getName());
                MyLog.d(TAG, "  - 用户白天主题判断结果: " + isInUse);
            }
        }

        MyLog.d(TAG, "  - 最终判断结果: " + isInUse);
        return isInUse;
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog() {
        if (themeInfo.getClassify() == 0) {
            MToast.makeTextShort("默认主题无法删除");
            return;
        }

        String title = "删除主题";
        String message = "确定要删除主题「" + themeInfo.getName() + "」吗？\n删除后将无法恢复。";

        if (isThemeCurrentlyInUse()) {
            message += "\n\n注意：删除当前使用的主题后，系统将自动切换到默认主题。";
        }

        // TODO: 实现删除确认对话框
        // 这里应该显示一个确认对话框，用户确认后调用deleteTheme()
        MyLog.d(TAG, "显示删除确认对话框: " + title + " - " + message);

        // 临时直接调用删除方法，实际应该在用户确认后调用
        deleteTheme();
    }

    /**
     * 检查点击防抖
     */
    private boolean checkClickDebounce() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastClickTime < CLICK_DEBOUNCE_TIME) {
            MyLog.d(TAG, "点击过于频繁，忽略本次点击");
            return false;
        }
        lastClickTime = currentTime;
        return true;
    }



    /**
     * 显示应用确认对话框
     */
    private void showApplyConfirmDialog() {
        if (isThemeCurrentlyInUse()) {
            MToast.makeTextShort("该主题已经在使用中");
            return;
        }

        String message = "确定要应用主题「" + themeInfo.getName() + "」吗？";

        // 检查是否需要切换模式
        int themeType = themeInfo.getThemeType();
        boolean isNightMode = SkinManager.getInstance().isNightModeNow();

        if ((themeType == 0 && !isNightMode) || (themeType == 1 && isNightMode)) {
            String modeText = themeType == 0 ? "夜间模式" : "白天模式";
            message += "\n\n应用后将自动切换到" + modeText;
        }

        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认应用主题: " + message);
        applyTheme();
    }

    /**
     * 应用主题
     */
    private void applyTheme() {
        if (isOperationInProgress) return;

        isOperationInProgress = true;
        updateButtonStates();

        try {
            int themeType = themeInfo.getThemeType();
            boolean isNightMode = SkinManager.getInstance().isNightModeNow();

            if (themeType == 0) {
                // 夜间主题
                registerAndLoadSkin();

                if (themeInfo.getClassify() == 0) {
                    // 默认夜间主题：设置为默认夜间主题
                    SettingsManager.setNightTheme(SettingsConstants.DEFAULT_NIGHT_THEME);
                } else {
                    // 用户夜间主题：设置为主题文件名
                    SettingsManager.setNightTheme(themeInfo.getName() + ".skin");
                }

                if (!isNightMode) {
                    SettingsManager.setThemeMode(2); // 切换到夜间模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用夜间主题: " + themeInfo.getName());

            } else if (themeType == 1) {
                // 白天主题
                registerAndLoadSkin();

                if (themeInfo.getClassify() == 0) {
                    // 默认白天主题：设置为空（表示使用默认）
                    SettingsManager.setDayTheme("");
                } else {
                    // 用户白天主题：设置为主题文件名
                    SettingsManager.setDayTheme(themeInfo.getName() + ".skin");
                }

                if (isNightMode) {
                    SettingsManager.setThemeMode(1); // 切换到白天模式
                }
                updateThemeUsageStatus();
                showSuccessMessage("已应用白天主题: " + themeInfo.getName());
            }
        } catch (Exception e) {
            MyLog.e(TAG, "应用主题失败", e);
            MToast.makeTextShort("应用主题失败，请重试");
        } finally {
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    /**
     * 注册并加载皮肤 - 修复默认主题处理
     */
    private void registerAndLoadSkin() {
        if (themeInfo.getClassify() == 0) {
            // 默认主题处理
            if (themeInfo.getThemeType() == 0) {
                // 默认夜间主题：加载默认夜间皮肤
                MyLog.d(TAG, "应用默认夜间主题");
                SkinManager.getInstance().loadSkin(SettingsConstants.DEFAULT_NIGHT_THEME);
            } else {
                // 默认白天主题：恢复默认白天主题
                MyLog.d(TAG, "应用默认白天主题");
                SkinManager.getInstance().restoreDefaultTheme();
            }
        } else {
            // 用户主题，加载皮肤文件
            String skinPath = themeInfo.getSinkUrl();
            if (skinPath != null && !skinPath.isEmpty()) {
                // 如果是网络URL，使用本地路径
                if (skinPath.startsWith("http")) {
                    String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
                    skinPath = absolutePath + "/skin/" + themeInfo.getName() + ".skin";
                }
                SkinManager.getInstance().registerFileSkin(skinPath);
                SkinManager.getInstance().loadSkin(themeInfo.getName() + ".skin");
            }
        }
        MyLog.v(TAG, "应用主题: " + themeInfo.getName() + ", 类型: " + (themeInfo.getThemeType() == 0 ? "夜间" : "白天"));
    }

    /**
     * 更新主题使用状态
     */
    private void updateThemeUsageStatus() {
        themeInfo.setUse(true);

        // 更新数据库（仅对非默认主题）
        if (themeInfo.getClassify() != 0) {
            disposables.add(
                dbManager.update(themeInfo)
                    .subscribe(
                        result -> {
                            MyLog.v(TAG, "主题使用状态更新成功");
                            updateActionBar();
                        },
                        throwable -> MyLog.e(TAG, "主题使用状态更新失败", throwable)
                    )
            );
        } else {
            updateActionBar();
        }
    }



    /**
     * 下载主题
     */
    private void downloadTheme() {
        if (isOperationInProgress || !NetworkUtils.isNetworkConnected(requireSceneContext())) {
            if (!NetworkUtils.isNetworkConnected(requireSceneContext())) {
                MToast.makeTextShort("网络连接不可用，请检查网络设置");
            }
            return;
        }

        isOperationInProgress = true;
        isDownloading = true;
        updateButtonStates();

        MyLog.d(TAG, "开始下载主题: " + themeInfo.getName());

        // 更新下载统计
        updateDownloadStats();

        // 开始真实下载（主题包信息已经在详情API中获取）
        downloadTimeMillis = String.valueOf(System.currentTimeMillis());
        startRealDownload();
    }

    /**
     * 更新下载统计 - 参考AllThemeFragment的updateDownloads方法
     */
    @SuppressLint("CheckResult")
    private void updateDownloadStats() {
        if (themeInfo == null || themeInfo.getClassify() == 0) {
            // 默认主题不需要更新下载统计
            return;
        }

        MyLog.d(TAG, "更新下载统计: " + themeInfo.getName());

        // 使用新API记录下载统计 - 根据API文档格式，传递下载信息
        String requestBody = "{\n" +
                "  \"packageId\": null,\n" +
                "  \"userId\": null,\n" +
                "  \"deviceId\": \"" + android.provider.Settings.Secure.getString(
                        requireSceneContext().getContentResolver(),
                        android.provider.Settings.Secure.ANDROID_ID) + "\",\n" +
                "  \"appVersion\": \"" + com.smartcar.easylauncher.BuildConfig.VERSION_NAME + "\",\n" +
                "  \"platform\": \"android\",\n" +
                "  \"downloadSource\": \"theme_detail\"\n" +
                "}";

        disposables.add(
                RxHttp.postJson(Const.NEW_THEME_DOWNLOAD + themeInfo.getId())
                        .addAll(requestBody)
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                responseString -> {
                                    try {
                                        Gson gson = new Gson();
                                        TypeToken<ApiResponse<Object>> typeToken = new TypeToken<ApiResponse<Object>>(){};
                                        ApiResponse<Object> response = gson.fromJson(responseString, typeToken.getType());

                                        if (response != null && response.isSuccess()) {
                                            MyLog.v(TAG, "新API下载统计更新成功: " + response.getMsg());
                                            // 更新本地显示的下载量
                                            themeInfo.setNumber(themeInfo.getNumber() + 1);
                                        } else {
                                            MyLog.w(TAG, "新API下载统计响应异常: " + (response != null ? response.getMsg() : "null"));
                                        }
                                    } catch (Exception e) {
                                        MyLog.e(TAG, "解析下载统计响应失败", e);
                                    }
                                },
                                throwable -> {
                                    MyLog.e(TAG, "新API下载统计更新失败", throwable);
                                    // 静默失败，不影响下载流程
                                }
                        )
        );
    }

    /**
     * 开始真实下载
     */
    private void startRealDownload() {
        // 文件存储路径
        String absolutePath = requireSceneContext().getFilesDir().getParentFile().getAbsolutePath();
        String destPath = absolutePath + "/skin/" + themeInfo.getName() + ".skin";

        MyLog.d(TAG, "下载路径: " + destPath);

        downloadDisposable = RxHttp.get(themeInfo.getSinkUrl())
                .tag(downloadTimeMillis)
                .toDownloadObservable(destPath)
                .onMainProgress(progress -> {
                    // 下载进度回调
                    int currentProgress = progress.getProgress(); // 当前进度 0-100
                    long currentSize = progress.getCurrentSize(); // 当前已下载的字节大小
                    long totalSize = progress.getTotalSize(); // 要下载的总字节大小

                    MyLog.v(TAG, "下载进度: " + currentProgress + "%");

                    // 更新进度显示
                    if (actionBar != null) {
                        actionBar.showProgress(currentProgress, "下载中...");
                    }
                })
                .subscribe(
                    downloadPath -> {
                        // 下载完成
                        MyLog.d(TAG, "下载完成: " + downloadPath);
                        onRealDownloadComplete(downloadPath);
                    },
                    throwable -> {
                        // 下载失败
                        MyLog.e(TAG, "下载失败", throwable);
                        onDownloadError(throwable);
                    }
                );

        // 添加到disposables中管理
        disposables.add(downloadDisposable);
    }


    /**
     * 真实下载完成处理
     */
    private void onRealDownloadComplete(String downloadPath) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        // 更新主题路径地址并保存到数据库
        themeInfo.setSinkUrl(downloadPath);
        disposables.add(
            dbManager.insert(themeInfo)
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题保存到数据库成功");
                        showSuccessMessage("主题下载完成");
                        // 更新操作栏状态
                        updateActionBar();
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题保存到数据库失败", throwable);
                        showErrorMessage("主题下载完成，但保存失败");
                        updateActionBar();
                    }
                )
        );
    }

    /**
     * 下载错误处理
     */
    private void onDownloadError(Throwable throwable) {
        isDownloading = false;
        isOperationInProgress = false;

        // 隐藏进度显示
        if (actionBar != null) {
            actionBar.hideProgress();
        }

        String errorMessage = "下载失败";
        if (throwable != null) {
            errorMessage += ": " + throwable.getMessage();
        }

        MyLog.e(TAG, errorMessage, throwable);
        showErrorMessage(errorMessage);

        // 更新操作栏状态
        updateActionBar();
    }


    /**
     * 删除主题
     */
    private void deleteTheme() {
        if (themeInfo.getClassify() == 0) {
            MToast.makeTextShort("默认主题无法删除");
            return;
        }

        // 删除主题文件
        boolean fileDeleted = FileUtils.deleteFolder(themeInfo.getSinkUrl());
        MyLog.v(TAG, "主题文件删除结果: " + fileDeleted);

        // 从数据库删除
        disposables.add(
            dbManager.deleteById(themeInfo.getId())
                .subscribe(
                    result -> {
                        MyLog.v(TAG, "主题数据库删除成功");
                        
                        // 如果删除的是当前使用的主题，重置为默认主题
                        if (isThemeCurrentlyInUse()) {
                            if (themeInfo.getThemeType() == 0) {
                                SettingsManager.setNightTheme("");
                            } else {
                                SettingsManager.setDayTheme("");
                            }
                        }
                        
                        MToast.makeTextShort("主题删除成功");
                        getNavigationScene(this).pop(); // 返回上一页
                    },
                    throwable -> {
                        MyLog.e(TAG, "主题数据库删除失败", throwable);
                        MToast.makeTextShort("主题删除失败");
                    }
                )
        );
    }





    /**
     * 显示错误消息
     */
    private void showErrorMessage(String message) {
        MToast.makeTextShort(message);
        MyLog.e(TAG, "错误: " + message);
    }


    /**
     * 检查主题更新 - 使用新API获取最新版本信息
     */
    @SuppressLint("CheckResult")
    private void checkThemeUpdate() {
        if (isCheckingUpdate || themeInfo == null || themeInfo.getClassify() == 0) {
            return;
        }

        isCheckingUpdate = true;
        MyLog.v(TAG, "检查主题更新: " + themeInfo.getName());

        // 使用新API检查特定主题的详情
        disposables.add(
                RxHttp.get(Const.NEW_THEME_DETAIL + themeInfo.getId())
                        .toObservable(String.class)
                        .observeOn(AndroidSchedulers.mainThread())
                        .subscribe(
                                this::handleNewThemeUpdateSuccessString,
                                this::handleNewThemeUpdateError
                        )
        );
    }

    /**
     * 处理服务器主题更新数据成功
     */
    private void handleServerThemeUpdateSuccess(ThemeListModel themeListModel) {
        isCheckingUpdate = false;

        if (themeListModel != null && themeListModel.getResults() != null) {
            // 查找当前主题的服务器版本
            for (ThemeListModel.Results serverTheme : themeListModel.getResults()) {
                if (serverTheme.getObjectId().equals(themeInfo.getId())) {
                    // 找到对应的服务器主题
                    latestServerTheme = convertServerToLocalModel(serverTheme);

                    // 比较版本号
                    boolean needUpdate = themeInfo.getVersionCode() < latestServerTheme.getVersionCode();

                    if (needUpdate != themeInfo.isUpdate()) {
                        themeInfo.setUpdate(needUpdate);

                        if (needUpdate) {
                            // 更新版本信息
                            themeInfo.setVersionName(latestServerTheme.getVersionName());
                            themeInfo.setUpdateContent(latestServerTheme.getUpdateContent());

                            MyLog.d(TAG, "发现主题更新: " + themeInfo.getName() +
                                    " 本地版本: " + themeInfo.getVersionCode() +
                                    " 服务器版本: " + latestServerTheme.getVersionCode());
                        }

                        // 刷新UI显示
                        updateActionBar();
                        showUpdateInfo();
                    }
                    break;
                }
            }
        }
    }

    /**
     * 处理服务器主题更新数据错误
     */
    private void handleServerThemeUpdateError(Throwable throwable) {
        isCheckingUpdate = false;
        MyLog.e(TAG, "检查主题更新失败", throwable);
        // 静默失败，不显示错误提示
    }

    /**
     * 转换服务器数据模型为本地数据模型
     */
    private ThemeInfoModel convertServerToLocalModel(ThemeListModel.Results serverTheme) {
        ThemeInfoModel localModel = new ThemeInfoModel();
        localModel.setId(serverTheme.getObjectId());
        localModel.setName(serverTheme.getName());
        localModel.setVersionCode(serverTheme.getVersionCode());
        localModel.setVersionName(serverTheme.getVersionName());
        localModel.setUpdateContent(serverTheme.getUpdateContent());
        localModel.setImg(serverTheme.getImg());
        localModel.setContent(serverTheme.getContent());
        localModel.setAuthor(serverTheme.getAuthor());
        localModel.setNumber(serverTheme.getNumber());
        localModel.setThemeType(serverTheme.getThemeType());
        localModel.setSinkUrl(serverTheme.getSinkUrl());
        localModel.setClassify(serverTheme.getClassify());
        return localModel;
    }

    /**
     * 显示更新确认对话框
     */
    private void showUpdateConfirmDialog() {
        if (latestServerTheme == null) {
            MToast.makeTextShort("获取更新信息失败，请重试");
            return;
        }

        String title = "更新主题";
        String message = "发现新版本 v" + latestServerTheme.getVersionName() +
                        "，确定要更新主题「" + themeInfo.getName() + "」吗？";

        if (latestServerTheme.getUpdateContent() != null && !latestServerTheme.getUpdateContent().trim().isEmpty()) {
            message += "\n\n更新内容：\n" + latestServerTheme.getUpdateContent();
        }

        // TODO: 使用项目统一的确认对话框
        // 这里暂时直接执行，实际项目中应该显示确认对话框
        MyLog.d(TAG, "确认更新主题: " + message);
        updateTheme();
    }

    /**
     * 更新主题 - 实际上是重新下载最新版本
     */
    private void updateTheme() {
        if (latestServerTheme == null) {
            MToast.makeTextShort("更新信息不完整，请重试");
            return;
        }

        // 更新主题信息为最新版本
        themeInfo.setVersionCode(latestServerTheme.getVersionCode());
        themeInfo.setVersionName(latestServerTheme.getVersionName());
        themeInfo.setUpdateContent(latestServerTheme.getUpdateContent());
        themeInfo.setSinkUrl(latestServerTheme.getSinkUrl());
        themeInfo.setUpdate(false); // 清除更新标记

        // 执行下载
        downloadTheme();
    }



    @Override
    public void onResume() {
        super.onResume();

        // 恢复轮播图自动播放
        if (currentBannerItemCount > 1) {
            binding.bannerViewPager.startLoop();
            MyLog.d(TAG, "页面恢复，自动轮播已启动");
        }

        // 重置操作状态（防止异常情况下状态未正确恢复）
        if (isOperationInProgress && !isDownloading) {
            MyLog.w(TAG, "检测到异常操作状态，重置状态");
            isOperationInProgress = false;
            updateButtonStates();
        }
    }

    @Override
    public void onPause() {
        super.onPause();

        // 暂停轮播图自动播放
        binding.bannerViewPager.stopLoop();
        MyLog.d(TAG, "页面暂停，自动轮播已停止");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 取消下载
        if (downloadDisposable != null && !downloadDisposable.isDisposed()) {
            downloadDisposable.dispose();
        }

        // 停止轮播图
        binding.bannerViewPager.stopLoop();

        // 清理资源
        disposables.clear();
        bannerAdapter = null;
        actionBar = null;
        binding = null;
    }

    /**
     * 处理新API主题更新检查成功 - 字符串响应版本
     */
    private void handleNewThemeUpdateSuccessString(String responseString) {
        isCheckingUpdate = false;

        MyLog.v(TAG, "新API主题更新检查响应: " + responseString);

//        try {
//            Gson gson = new Gson();
//            TypeToken<ApiResponse<NewThemeInfo>> typeToken = new TypeToken<ApiResponse<NewThemeInfo>>(){};
//            ApiResponse<NewThemeInfo> response = gson.fromJson(responseString, typeToken.getType());
//
//            if (response != null && response.isSuccess() && response.getData() != null) {
//                NewThemeInfo serverThemeInfo = response.getData();
//                MyLog.v(TAG, "新API主题更新检查解析成功: " + serverThemeInfo.getThemeName());
//
//                // 检查是否有可用的主题包
//                if (serverThemeInfo.getThemePackages() != null && !serverThemeInfo.getThemePackages().isEmpty()) {
//                    // 找到最新版本的主题包
//                    NewThemePackage latestPackage = serverThemeInfo.getThemePackages().stream()
//                            .filter(pkg -> pkg.getIsLatest() != null && pkg.getIsLatest() == 1)
//                            .findFirst()
//                            .orElse(serverThemeInfo.getThemePackages().get(0));
//
//                    // 比较版本号 - versionCode现在是Integer类型
//                    int serverVersionCode = latestPackage.getVersionCode() != null ? latestPackage.getVersionCode() : 1;
//                    boolean needUpdate = themeInfo.getVersionCode() < serverVersionCode;
//
//                    if (needUpdate != themeInfo.isUpdate()) {
//                        themeInfo.setUpdate(needUpdate);
//
//                        // 更新数据库中的更新状态
//                        updateThemeUpdateStatus(needUpdate);
//
//                        MyLog.d(TAG, String.format("主题 %s 更新状态: %s (本地版本: %d, 服务器版本: %d)",
//                                themeInfo.getName(), needUpdate ? "需要更新" : "已是最新",
//                                themeInfo.getVersionCode(), serverVersionCode));
//                    }
//                }
//            } else {
//                MyLog.w(TAG, "新API主题更新检查响应异常: " + (response != null ? response.getMsg() : "null"));
//            }
//        } catch (Exception e) {
//            MyLog.e(TAG, "解析新API主题更新检查响应失败", e);
//        }
    }





    /**
     * 处理新API主题更新检查失败
     */
    private void handleNewThemeUpdateError(Throwable throwable) {
        isCheckingUpdate = false;
        MyLog.e(TAG, "新API主题更新检查失败", throwable);
        // 静默失败，不影响用户体验
    }



    /**
     * 更新主题的更新状态到数据库
     */
    private void updateThemeUpdateStatus(boolean needUpdate) {
        if (dbManager != null) {
            themeInfo.setUpdate(needUpdate);
            disposables.add(
                    dbManager.update(themeInfo)
                            .subscribe(
                                    result -> MyLog.d(TAG, "主题更新状态已保存到数据库"),
                                    throwable -> MyLog.e(TAG, "保存主题更新状态失败", throwable)
                            )
            );
        }
    }
}
