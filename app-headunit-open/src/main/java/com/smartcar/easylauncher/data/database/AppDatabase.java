package com.smartcar.easylauncher.data.database;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.smartcar.easylauncher.app.App;
import com.smartcar.easylauncher.data.database.dao.CarDao;
import com.smartcar.easylauncher.data.database.dao.ExpenseDao;
import com.smartcar.easylauncher.data.database.dao.GPSPointDao;
import com.smartcar.easylauncher.data.database.dao.KeyMapDao;
import com.smartcar.easylauncher.data.database.dao.NewThemeDao;
import com.smartcar.easylauncher.data.database.dao.ThemeInfoDao;
import com.smartcar.easylauncher.data.database.dao.TripDao;
import com.smartcar.easylauncher.data.database.dao.TpmsDao;
import com.smartcar.easylauncher.data.database.dao.UnifiedThemeDaoSimple;
import com.smartcar.easylauncher.data.database.entity.CarModel;
import com.smartcar.easylauncher.data.database.entity.Expense;
import com.smartcar.easylauncher.data.database.entity.GPSPointModel;
import com.smartcar.easylauncher.data.database.entity.KeyMapModel;
import com.smartcar.easylauncher.data.database.entity.NewThemeEntity;
import com.smartcar.easylauncher.data.database.entity.ThemeInfoModel;
import com.smartcar.easylauncher.data.database.entity.TripModel;
import com.smartcar.easylauncher.data.database.entity.TpmsEntity;
import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;
import com.smartcar.easylauncher.data.database.converter.ThemeTypeConverters;
import com.smartcar.easylauncher.shared.utils.MyLog;

/**
 * entities表示要包含哪些表；version为数据库的版本，数据库升级时更改；exportSchema是否导出数据库结构，默认为true,
 * <AUTHOR>
 * @desc 数据库操作工具类
 */
@Database(entities = {Expense.class, CarModel.class, ThemeInfoModel.class, TripModel.class, GPSPointModel.class, TpmsEntity.class, KeyMapModel.class, NewThemeEntity.class, UnifiedThemeModel.class}, version = 10, exportSchema = false)
@TypeConverters({ThemeTypeConverters.class})
public abstract class AppDatabase extends RoomDatabase {
    private static final String TAG = "AppDatabase";
    private static final String DATABASE_NAME = "smartcar.db";
    private static volatile AppDatabase instance;

    /**
     * 抽象方法，返回ThemeInfoDao对象
     */
    public abstract ThemeInfoDao getThemeInfoDao();

    /**
     * 抽象方法，返回NewThemeDao对象
     */
    public abstract NewThemeDao getNewThemeDao();

    /**
     * 抽象方法，返回UnifiedThemeDaoSimple对象
     */
    public abstract UnifiedThemeDaoSimple getUnifiedThemeDao();
    /**;'
     * 抽象方法，返回TripDao对象
     */
    public abstract TripDao getTripDao();

    /**
     * 抽象方法，返回GPSPointDao对象
     */
    public abstract GPSPointDao getGpsPointDao();

    /**
     * 抽象方法，返回ExpenseDao对象
     */
    public abstract ExpenseDao getExpenseDao();

    /**
     * 抽象方法，返回CarDao对象
     */
    public abstract CarDao getCarDao();

    /**
     * 抽象方法，返回TpmsDao对象
     * 用于访问TPMS（胎压监测系统）相关的数据库操作
     */
    public abstract TpmsDao getTpmsDao();

    /**
     * 抽象方法，返回KeyMapDao对象
     * 用于访问按键映射相关的数据库操作
     */
    public abstract KeyMapDao getKeyMapDao();

    public static AppDatabase getDatabase() {
        if (instance == null) {
            synchronized (AppDatabase.class) {
                if (instance == null) {
                    instance = Room.databaseBuilder(
                            App.getContextInstance(),
                            AppDatabase.class,
                            DATABASE_NAME)
                            .addMigrations(MIGRATION_1_2,MIGRATION_2_3, MIGRATION_3_4, MIGRATION_4_5, MIGRATION_5_6, MIGRATION_6_7, MIGRATION_7_8, MIGRATION_8_9, MIGRATION_9_10) //升级数据库
                            .fallbackToDestructiveMigration()  // 添加这行，允许在找不到迁移路径时重建数据库
                            .build();
                    MyLog.d(TAG, "Database instance created");
                }
            }
        }
        return instance;
    }

    /**
     * 数据库升级  version1 -> version2
     */
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //向themeInfo表中添加一个sport字段
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN isUse INTEGER");
        }
    };

    /**
     * 数据库升级  version2 -> version3  物理
     */
    static final Migration MIGRATION_2_3 = new Migration(2, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //向themeInfo表中添加一个physical字段
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN `themeType` INTEGER NOT NULL DEFAULT 1");
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN `label` TEXT");
            //   database.execSQL("ALTER TABLE themeInfo ADD COLUMN physical INTEGER");
        }
    };

    /**
     * 数据库升级 version3 -> version4  物理
     */
    static final Migration MIGRATION_3_4 = new Migration(3, 4) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //向cars表中添加一个brand_logo字段
            database.execSQL("ALTER TABLE cars ADD COLUMN brand_logo TEXT");
        }
    };

    /**
     * 数据库升级  version2 -> version3  物理
     */
    static final Migration MIGRATION_1_3 = new Migration(1, 3) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            //创建新表
            database.execSQL("CREATE TABLE IF NOT EXISTS themeInfo_new(id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," +
                    "age INTEGER NOT NULL,name TEXT,chinese INTEGER,english INTEGER,math INTEGER,sport INTEGER,physical INTEGER)");
            //备份数据
            database.execSQL("INSERT INTO themeInfo_new(id,age,name,chinese,english,math) SELECT id,age,name,chinese,english,math FROM themeInfo");
            //删除旧表
            database.execSQL("DROP TABLE themeInfo");
            //重命名表
            database.execSQL("ALTER TABLE themeInfo_new RENAME TO themeInfo");

        }
    };

    /**
     * 数据库升级 version4 -> version5
     * 处理trips表中userId字段类型变更
     */
    static final Migration MIGRATION_4_5 = new Migration(4, 5) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 1. 创建新表
            database.execSQL(
                "CREATE TABLE IF NOT EXISTS trips_new (" +
                "id INTEGER NOT NULL PRIMARY KEY," +
                "startTime INTEGER NOT NULL," +
                "endTime INTEGER NOT NULL," +
                "vehicleId TEXT," +
                "userId INTEGER," +  // 改为INTEGER类型
                "type INTEGER NOT NULL DEFAULT 0," +
                "totalDistance REAL NOT NULL DEFAULT 0," +
                "totalTime INTEGER NOT NULL DEFAULT 0," +
                "activeTime INTEGER NOT NULL DEFAULT 0," +
                "maxAltitude REAL NOT NULL DEFAULT 0," +
                "minAltitude REAL NOT NULL DEFAULT 0," +
                "avgAltitude REAL NOT NULL DEFAULT 0," +
                "maxSpeed REAL NOT NULL DEFAULT 0," +
                "avgSpeed REAL NOT NULL DEFAULT 0," +
                "maxAcceleration REAL NOT NULL DEFAULT 0," +
                "startLatitude REAL NOT NULL DEFAULT 0," +
                "startLongitude REAL NOT NULL DEFAULT 0," +
                "endLatitude REAL NOT NULL DEFAULT 0," +
                "endLongitude REAL NOT NULL DEFAULT 0," +
                "serverId INTEGER," +
                "uploaded INTEGER NOT NULL DEFAULT 0," +
                "serverTripId TEXT," +
                "lastUploadTime INTEGER NOT NULL DEFAULT 0" +
                ")"
            );

            // 2. 复制数据，将userId转换为INTEGER
            database.execSQL(
                "INSERT INTO trips_new " +
                "SELECT id, startTime, endTime, vehicleId, " +
                "CAST(CASE WHEN userId IS NULL THEN 0 ELSE userId END AS INTEGER), " +  // 处理NULL值
                "type, totalDistance, totalTime, activeTime, " +
                "maxAltitude, minAltitude, avgAltitude, maxSpeed, avgSpeed, " +
                "maxAcceleration, startLatitude, startLongitude, endLatitude, endLongitude, " +
                "serverId, uploaded, serverTripId, lastUploadTime " +
                "FROM trips"
            );

            // 3. 删除旧表
            database.execSQL("DROP TABLE trips");

            // 4. 重命名新表
            database.execSQL("ALTER TABLE trips_new RENAME TO trips");
        }
    };

    /**
     * 数据库升级 version5 -> version6
     * 处理expenses表中字段改名为total_meters的迁移
     */
    static final Migration MIGRATION_5_6 = new Migration(5, 6) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 1. 创建新表
            database.execSQL(
                "CREATE TABLE IF NOT EXISTS expenses_new (" +
                "expenseId INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," +
                "title TEXT NOT NULL," +
                "type INTEGER NOT NULL," +
                "date TEXT NOT NULL," +
                "spent REAL NOT NULL," +
                "description TEXT," +
                "price_per_liter REAL," +
                "total_meters INTEGER," +  // 新字段名
                "carId INTEGER NOT NULL," +
                "userId INTEGER NOT NULL" +
                ")"
            );

            // 2. 复制基本数据，total_meters设为NULL
            database.execSQL(
                "INSERT INTO expenses_new (expenseId, title, type, date, spent, description, price_per_liter, total_meters, carId, userId) " +
                "SELECT expenseId, title, type, date, spent, description, price_per_liter, NULL, carId, userId " +
                "FROM expenses"
            );

            // 3. 删除旧表
            database.execSQL("DROP TABLE expenses");

            // 4. 重命名新表
            database.execSQL("ALTER TABLE expenses_new RENAME TO expenses");
        }
    };
    
    /**
     * 数据库升级 version6 -> version7
     * 添加key_mappings表，用于存储按键映射
     */
    static final Migration MIGRATION_6_7 = new Migration(6, 7) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 创建key_mappings表
            database.execSQL(
                "CREATE TABLE IF NOT EXISTS key_mappings (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL," +
                "key_code INTEGER NOT NULL," +
                "key_name TEXT," +
                "action_type INTEGER NOT NULL," +
                "action_data TEXT," +
                "extra_data TEXT," +
                "description TEXT," +
                "enabled INTEGER NOT NULL DEFAULT 1," +
                "create_time INTEGER NOT NULL," +
                "update_time INTEGER NOT NULL" +
                ")"
            );
        }
    };

    /**
     * 数据库升级 version7 -> version8 - 添加新API支持字段
     */
    static final Migration MIGRATION_7_8 = new Migration(7, 8) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 向themeInfo表中添加新API支持字段
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN apiSource TEXT DEFAULT 'old_api'");
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN newApiId INTEGER");
            database.execSQL("ALTER TABLE themeInfo ADD COLUMN previewImagesJson TEXT");
            MyLog.d(TAG, "数据库升级到版本8完成，添加新API支持字段");
        }
    };

    /**
     * 数据库升级 version8 -> version9 - 创建新主题表
     */
    static final Migration MIGRATION_8_9 = new Migration(8, 9) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 创建新主题表
            database.execSQL("CREATE TABLE IF NOT EXISTS `new_theme` (" +
                    "`id` INTEGER PRIMARY KEY NOT NULL, " +
                    "`theme_name` TEXT, " +
                    "`theme_description` TEXT, " +
                    "`theme_type` INTEGER, " +
                    "`download_count` INTEGER, " +
                    "`heat` INTEGER, " +
                    "`author_id` INTEGER, " +
                    "`author` TEXT, " +
                    "`author_img` TEXT, " +
                    "`price` REAL, " +
                    "`is_trial_enabled` INTEGER, " +
                    "`status` INTEGER, " +
                    "`release_status` INTEGER, " +
                    "`is_vip` INTEGER, " +
                    "`label` TEXT, " +
                    "`cover_image` TEXT, " +
                    "`sort_order` INTEGER, " +
                    "`create_time` TEXT, " +
                    "`update_time` TEXT, " +
                    "`remark` TEXT, " +
                    "`preview_images_json` TEXT, " +
                    "`packages_json` TEXT, " +
                    "`is_downloaded` INTEGER NOT NULL DEFAULT 0, " +
                    "`is_installed` INTEGER NOT NULL DEFAULT 0, " +
                    "`is_in_use` INTEGER NOT NULL DEFAULT 0, " +
                    "`local_file_path` TEXT, " +
                    "`download_time` TEXT, " +
                    "`install_time` TEXT, " +
                    "`current_version_name` TEXT, " +
                    "`current_version_code` TEXT, " +
                    "`download_url` TEXT, " +
                    "`has_update` INTEGER NOT NULL DEFAULT 0)");

            MyLog.d(TAG, "数据库升级到版本9完成，创建新主题表");
        }
    };

    /**
     * 数据库升级 version9 -> version10 - 创建统一主题表
     */
    static final Migration MIGRATION_9_10 = new Migration(9, 10) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // 创建统一主题表
            database.execSQL("CREATE TABLE IF NOT EXISTS `unified_themes` (" +
                    "`id` INTEGER PRIMARY KEY NOT NULL, " +
                    "`create_by` TEXT, " +
                    "`create_time` TEXT, " +
                    "`update_by` TEXT, " +
                    "`update_time` TEXT, " +
                    "`theme_name` TEXT, " +
                    "`theme_description` TEXT, " +
                    "`theme_type` INTEGER, " +
                    "`theme_type_name` TEXT, " +
                    "`download_count` INTEGER, " +
                    "`heat` INTEGER, " +
                    "`author_id` INTEGER, " +
                    "`author` TEXT, " +
                    "`author_img` TEXT, " +
                    "`price` REAL, " +
                    "`is_trial_enabled` INTEGER, " +
                    "`status` INTEGER, " +
                    "`release_status` INTEGER, " +
                    "`is_vip` INTEGER, " +
                    "`label` TEXT, " +
                    "`cover_image` TEXT, " +
                    "`sort_order` INTEGER, " +
                    "`preview_images` TEXT, " +
                    "`theme_package` TEXT, " +
                    "`is_downloaded` INTEGER NOT NULL DEFAULT 0, " +
                    "`local_file_path` TEXT, " +
                    "`is_current_theme` INTEGER NOT NULL DEFAULT 0, " +
                    "`download_time` TEXT)");

            MyLog.d(TAG, "数据库升级到版本10完成，创建统一主题表");
        }
    };
}
