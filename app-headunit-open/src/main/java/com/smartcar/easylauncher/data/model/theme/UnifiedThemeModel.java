package com.smartcar.easylauncher.data.model.theme;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.smartcar.easylauncher.data.database.converter.ThemeTypeConverters;

import java.util.List;

/**
 * 统一的主题数据模型
 * 完全对应你提供的JSON数据结构，同时支持：
 * 1. API响应解析 (Gson)
 * 2. 数据库存储 (Room)
 * 3. 业务逻辑使用
 * 
 * 避免了多个模型之间的转换，简化代码
 */
@Entity(tableName = "unified_themes")
@TypeConverters(ThemeTypeConverters.class)
public class UnifiedThemeModel {

    @PrimaryKey
    @SerializedName("id")
    @ColumnInfo(name = "id")
    private Long id;

    @SerializedName("createBy")
    @ColumnInfo(name = "create_by")
    private String createBy;

    @SerializedName("createTime")
    @ColumnInfo(name = "create_time")
    private String createTime;

    @SerializedName("updateBy")
    @ColumnInfo(name = "update_by")
    private String updateBy;

    @SerializedName("updateTime")
    @ColumnInfo(name = "update_time")
    private String updateTime;

    @SerializedName("themeName")
    @ColumnInfo(name = "theme_name")
    private String themeName;

    @SerializedName("themeDescription")
    @ColumnInfo(name = "theme_description")
    private String themeDescription;

    @SerializedName("themeType")
    @ColumnInfo(name = "theme_type")
    private Integer themeType;

    @SerializedName("themeTypeName")
    @ColumnInfo(name = "theme_type_name")
    private String themeTypeName;

    @SerializedName("downloadCount")
    @ColumnInfo(name = "download_count")
    private Long downloadCount;

    @SerializedName("heat")
    @ColumnInfo(name = "heat")
    private Long heat;

    @SerializedName("authorId")
    @ColumnInfo(name = "author_id")
    private Long authorId;

    @SerializedName("author")
    @ColumnInfo(name = "author")
    private String author;

    @SerializedName("authorImg")
    @ColumnInfo(name = "author_img")
    private String authorImg;

    @SerializedName("price")
    @ColumnInfo(name = "price")
    private Double price;

    @SerializedName("isTrialEnabled")
    @ColumnInfo(name = "is_trial_enabled")
    private Integer isTrialEnabled;

    @SerializedName("status")
    @ColumnInfo(name = "status")
    private Integer status;

    @SerializedName("releaseStatus")
    @ColumnInfo(name = "release_status")
    private Integer releaseStatus;

    @SerializedName("isVip")
    @ColumnInfo(name = "is_vip")
    private Integer isVip;

    @SerializedName("label")
    @ColumnInfo(name = "label")
    private String label;

    @SerializedName("coverImage")
    @ColumnInfo(name = "cover_image")
    private String coverImage;

    @SerializedName("sortOrder")
    @ColumnInfo(name = "sort_order")
    private Integer sortOrder;

    // 复杂对象使用TypeConverter自动转换
    @SerializedName("previewImages")
    @ColumnInfo(name = "preview_images")
    private List<PreviewImage> previewImages;

    @SerializedName("themePackage")
    @ColumnInfo(name = "theme_package")
    private ThemePackage themePackage;

    // 本地数据库字段，API不返回
    @Expose(serialize = false, deserialize = false)
    @ColumnInfo(name = "is_downloaded")
    private Boolean isDownloaded = false;

    @Expose(serialize = false, deserialize = false)
    @ColumnInfo(name = "local_file_path")
    private String localFilePath;

    @Expose(serialize = false, deserialize = false)
    @ColumnInfo(name = "is_current_theme")
    private Boolean isCurrentTheme = false;

    @Expose(serialize = false, deserialize = false)
    @ColumnInfo(name = "download_time")
    private String downloadTime;

    // 构造函数
    public UnifiedThemeModel() {}

    // 基本字段的getter/setter
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getCreateBy() { return createBy; }
    public void setCreateBy(String createBy) { this.createBy = createBy; }

    public String getCreateTime() { return createTime; }
    public void setCreateTime(String createTime) { this.createTime = createTime; }

    public String getUpdateBy() { return updateBy; }
    public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

    public String getUpdateTime() { return updateTime; }
    public void setUpdateTime(String updateTime) { this.updateTime = updateTime; }

    public String getThemeName() { return themeName; }
    public void setThemeName(String themeName) { this.themeName = themeName; }

    public String getThemeDescription() { return themeDescription; }
    public void setThemeDescription(String themeDescription) { this.themeDescription = themeDescription; }

    public Integer getThemeType() { return themeType; }
    public void setThemeType(Integer themeType) { this.themeType = themeType; }

    public String getThemeTypeName() { return themeTypeName; }
    public void setThemeTypeName(String themeTypeName) { this.themeTypeName = themeTypeName; }

    public Long getDownloadCount() { return downloadCount; }
    public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }

    public Long getHeat() { return heat; }
    public void setHeat(Long heat) { this.heat = heat; }

    public Long getAuthorId() { return authorId; }
    public void setAuthorId(Long authorId) { this.authorId = authorId; }

    public String getAuthor() { return author; }
    public void setAuthor(String author) { this.author = author; }

    public String getAuthorImg() { return authorImg; }
    public void setAuthorImg(String authorImg) { this.authorImg = authorImg; }

    public Double getPrice() { return price; }
    public void setPrice(Double price) { this.price = price; }

    public Integer getIsTrialEnabled() { return isTrialEnabled; }
    public void setIsTrialEnabled(Integer isTrialEnabled) { this.isTrialEnabled = isTrialEnabled; }

    public Integer getStatus() { return status; }
    public void setStatus(Integer status) { this.status = status; }

    public Integer getReleaseStatus() { return releaseStatus; }
    public void setReleaseStatus(Integer releaseStatus) { this.releaseStatus = releaseStatus; }

    public Integer getIsVip() { return isVip; }
    public void setIsVip(Integer isVip) { this.isVip = isVip; }

    public String getLabel() { return label; }
    public void setLabel(String label) { this.label = label; }

    public String getCoverImage() { return coverImage; }
    public void setCoverImage(String coverImage) { this.coverImage = coverImage; }

    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }

    public List<PreviewImage> getPreviewImages() { return previewImages; }
    public void setPreviewImages(List<PreviewImage> previewImages) { this.previewImages = previewImages; }

    public ThemePackage getThemePackage() { return themePackage; }
    public void setThemePackage(ThemePackage themePackage) { this.themePackage = themePackage; }

    // 本地字段的getter/setter
    public Boolean getIsDownloaded() { return isDownloaded; }
    public void setIsDownloaded(Boolean isDownloaded) { this.isDownloaded = isDownloaded; }

    public String getLocalFilePath() { return localFilePath; }
    public void setLocalFilePath(String localFilePath) { this.localFilePath = localFilePath; }

    public Boolean getIsCurrentTheme() { return isCurrentTheme; }
    public void setIsCurrentTheme(Boolean isCurrentTheme) { this.isCurrentTheme = isCurrentTheme; }

    public String getDownloadTime() { return downloadTime; }
    public void setDownloadTime(String downloadTime) { this.downloadTime = downloadTime; }

    // 内部类 - 预览图片
    public static class PreviewImage {
        @SerializedName("createBy")
        private String createBy;
        
        @SerializedName("createTime")
        private String createTime;
        
        @SerializedName("updateBy")
        private String updateBy;
        
        @SerializedName("id")
        private Long id;
        
        @SerializedName("themeId")
        private Long themeId;
        
        @SerializedName("title")
        private String title;
        
        @SerializedName("description")
        private String description;
        
        @SerializedName("imageUrl")
        private String imageUrl;
        
        @SerializedName("sortOrder")
        private Integer sortOrder;
        
        @SerializedName("imageType")
        private Integer imageType;

        // getter/setter方法
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getThemeId() { return themeId; }
        public void setThemeId(Long themeId) { this.themeId = themeId; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public Integer getSortOrder() { return sortOrder; }
        public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }

        public Integer getImageType() { return imageType; }
        public void setImageType(Integer imageType) { this.imageType = imageType; }
    }

    // 内部类 - 主题包
    public static class ThemePackage {
        @SerializedName("createBy")
        private String createBy;
        
        @SerializedName("createTime")
        private String createTime;
        
        @SerializedName("updateBy")
        private String updateBy;
        
        @SerializedName("id")
        private Long id;
        
        @SerializedName("themeId")
        private Long themeId;
        
        @SerializedName("versionCode")
        private Integer versionCode;
        
        @SerializedName("versionName")
        private String versionName;
        
        @SerializedName("packagePath")
        private String packagePath;
        
        @SerializedName("fileName")
        private String fileName;
        
        @SerializedName("fileSize")
        private Long fileSize;
        
        @SerializedName("updateDescription")
        private String updateDescription;
        
        @SerializedName("status")
        private Integer status;
        
        @SerializedName("statusName")
        private String statusName;
        
        @SerializedName("publishTime")
        private String publishTime;
        
        @SerializedName("downloadCount")
        private Long downloadCount;
        
        @SerializedName("isLatest")
        private Integer isLatest;
        
        @SerializedName("downloadUrl")
        private String downloadUrl;

        // getter/setter方法省略，需要时可以添加
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }

        public String getCreateTime() { return createTime; }
        public void setCreateTime(String createTime) { this.createTime = createTime; }

        public String getUpdateBy() { return updateBy; }
        public void setUpdateBy(String updateBy) { this.updateBy = updateBy; }

        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }

        public Long getThemeId() { return themeId; }
        public void setThemeId(Long themeId) { this.themeId = themeId; }

        public Integer getVersionCode() { return versionCode; }
        public void setVersionCode(Integer versionCode) { this.versionCode = versionCode; }

        public String getVersionName() { return versionName; }
        public void setVersionName(String versionName) { this.versionName = versionName; }

        public String getPackagePath() { return packagePath; }
        public void setPackagePath(String packagePath) { this.packagePath = packagePath; }

        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }

        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }

        public String getUpdateDescription() { return updateDescription; }
        public void setUpdateDescription(String updateDescription) { this.updateDescription = updateDescription; }

        public Integer getStatus() { return status; }
        public void setStatus(Integer status) { this.status = status; }

        public String getStatusName() { return statusName; }
        public void setStatusName(String statusName) { this.statusName = statusName; }

        public String getPublishTime() { return publishTime; }
        public void setPublishTime(String publishTime) { this.publishTime = publishTime; }

        public Long getDownloadCount() { return downloadCount; }
        public void setDownloadCount(Long downloadCount) { this.downloadCount = downloadCount; }

        public Integer getIsLatest() { return isLatest; }
        public void setIsLatest(Integer isLatest) { this.isLatest = isLatest; }

        public String getDownloadUrl() { return downloadUrl; }
        public void setDownloadUrl(String downloadUrl) { this.downloadUrl = downloadUrl; }
    }
}
