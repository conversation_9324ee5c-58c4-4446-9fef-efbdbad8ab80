package com.smartcar.easylauncher.data.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.smartcar.easylauncher.data.model.theme.UnifiedThemeModel;

import java.util.List;

/**
 * 简化版统一主题数据访问对象
 * 只包含基本的CRUD操作，避免编译时SQL验证问题
 */
@Dao
public interface UnifiedThemeDaoSimple {

    // -------------------------插入操作--------------------------------------------

    /**
     * 插入单个主题（冲突时替换）
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Long insert(UnifiedThemeModel theme);

    /**
     * 批量插入主题
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    List<Long> insert(List<UnifiedThemeModel> themes);

    // -------------------------更新操作--------------------------------------------

    /**
     * 更新主题
     */
    @Update
    int update(UnifiedThemeModel theme);

    // -------------------------删除操作--------------------------------------------

    /**
     * 删除主题
     */
    @Delete
    int delete(UnifiedThemeModel theme);

    /**
     * 根据ID删除主题
     */
    @Query("DELETE FROM unified_themes WHERE id = :themeId")
    int deleteById(Long themeId);

    /**
     * 清空所有主题
     */
    @Query("DELETE FROM unified_themes")
    void deleteAll();

    // -------------------------查询操作--------------------------------------------

    /**
     * 根据ID查询主题
     */
    @Query("SELECT * FROM unified_themes WHERE id = :themeId")
    UnifiedThemeModel getById(Long themeId);

    /**
     * 查询所有主题
     */
    @Query("SELECT * FROM unified_themes ORDER BY create_time DESC")
    List<UnifiedThemeModel> getAll();

    /**
     * 统计主题数量
     */
    @Query("SELECT COUNT(*) FROM unified_themes")
    int getThemeCount();
}
